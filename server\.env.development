DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=root
DB_DATABASE=lead_ms
DB_SYNCHRONIZE=true
DB_SSL=false

# Admin Credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Admin123
ADMIN_USERNAME=Yathu

JWT_SECRET=b20b9ddd56a566ca2f0376a56f3d26e30c7ac904108c6b000da5f20adf4c1a2ed834580c7aab0fdefd41f212b03a1606
JWT_EXPIRATION=1d
JWT_ACCESS_SECRET=7f9bbd2b9ebe9bd07b6a959e0abe7b667ae7783f2d71e189780d10db8192a8f86b412e4a3a3c36c5b8d0976bb4b219fc
JWT_REFRESH_SECRET=798ad8c237a22f042b6fd742afde5eabbb367cc19405ac7a55e3378452bc160831fc1142c00e1c2d4a007a9aca1b08a2
JWT_REFRESH_EXPIRATION=7d

CLOUDINARY_CLOUD_NAME=dytx4wqfa
CLOUDINARY_API_KEY=753424149517315
CLOUDINARY_API_SECRET=btF8ZhjZ9N4Hlj0iMG8k13Yw3C8

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=Alkxr4rHBH9UuvM2Vp5qUnugEYpBlpty

LOGIN_URL=http://localhost:3001

EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=somhtexibcxkvdkq
EMAIL_FROM=<EMAIL>

API_PREFIX=api/v1

NODE_ENV=development
PORT=3000


# The SameSite attribute for cookies. Possible values: "none", "lax", "strict"
COOKIE_SAMESITE=lax
