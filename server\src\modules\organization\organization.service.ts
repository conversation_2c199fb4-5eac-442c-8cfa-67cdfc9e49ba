import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrganizationConfig } from './entities/organization_configs.entity';
import { Organization } from './entities/organizations.entity';
import { CreateOrganizationDto } from './create-organization.dto';
import { OrganizationSubscription } from '../subscriptions/entities/organization_subscriptions.entity';
import { SubscriptionPlan } from '../subscriptions/entities/subscription_plans.entity';

@Injectable()
export class OrganizationService {
  constructor(
    @InjectRepository(OrganizationConfig)
    private orgConfigRepo: Repository<OrganizationConfig>,
    @InjectRepository(Organization)
    private organizationRepo: Repository<Organization>,
    @InjectRepository(OrganizationSubscription)
    private organizationSubscriptionRepo: Repository<OrganizationSubscription>,
    @InjectRepository(SubscriptionPlan)
    private subscriptionPlanRepo: Repository<SubscriptionPlan>,
    @InjectRepository(Organization)
    private organizationRepository: Repository<Organization>,
  ) {}

  async findOrgBySubdomain(subdomain: string): Promise<Organization | null> {
    return this.organizationRepo.findOne({
      where: {
        config: {
          loginUrlSubdomain: subdomain,
        },
      },
      relations: ['config'],
    });
  }

  async createOrganizationWithSubscription(
    createOrganizationDto: CreateOrganizationDto,
  ): Promise<Organization> {
    const { subscriptionPlanId, startDate, endDate, isTrial, ...orgData } =
      createOrganizationDto;

    // 1. Validate the subscription plan
    const subscriptionPlan = await this.subscriptionPlanRepo.findOne({
      where: { id: subscriptionPlanId },
    });
    if (!subscriptionPlan)
      throw new BadRequestException('Invalid subscription plan');

    // 2. Create and save organization
    const organization = this.organizationRepo.create(orgData);
    await this.organizationRepo.save(organization);

    // 3. Create and save organization config (default values or extendable)
    const config = this.orgConfigRepo.create({
      organization: organization, // pass the full organization entity
      loginUrlSubdomain: organization.name.toLowerCase().replace(/\s+/g, '-'), // e.g., 'oxford-academy'
      emailFromName: organization.name,
      brandingLogoUrl: undefined, // use undefined instead of null for nullable string
      timezone: 'Asia/Kolkata',
      language: 'en',
    });
    await this.orgConfigRepo.save(config);

    // 4. Create and save organization subscription
    const orgSub = this.organizationSubscriptionRepo.create({
      organization: { id: organization.id },
      subscriptionPlan: { id: subscriptionPlan.id },
      startDate,
      endDate,
      isActive: true,
      paymentStatus: 'success',
      isTrial: !!isTrial,
      trialStartDate: isTrial ? startDate : null,
      trialEndDate: isTrial ? endDate : null,
    });
    await this.organizationSubscriptionRepo.save(orgSub);

    return organization;
  }
}
