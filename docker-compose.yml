services:
  # backend:
  #   build:
  #     context: ./server
  #     dockerfile: Dockerfile
  #   ports:
  #     - "3000:3000"
  #   environment:
  #     - NODE_ENV=production
  #     - DATABASE_HOST=postgres
  #     - DATABASE_PORT=5432
  #     - DATABASE_USER=${POSTGRES_USER}
  #     - DATABASE_PASSWORD=${POSTGRES_PASSWORD}
  #     - DATABASE_NAME=${POSTGRES_DB}
  #     - REDIS_HOST=redis
  #     - REDIS_PORT=6379
  #     - REDIS_PASSWORD=${REDIS_PASSWORD}
  #   depends_on:
  #     - redis
  #   restart: unless-stopped
  #   networks:
  #     - app-network

  # frontend:
  #   build:
  #     context: ./client
  #     dockerfile: Dockerfile
  #   ports:
  #     - "3001:3001"
  #   environment:
  #     - NODE_ENV=production
  #     - NEXT_PUBLIC_API_URL=http://backend:3000
  #   depends_on:
  #     - backend
  #   restart: unless-stopped
  #   networks:
  #     - app-network

  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    restart: unless-stopped
    networks:
      - app-network

  # postgres:
  #   image: postgres:15-alpine
  #   ports:
  #     - "5433:5432"
  #   environment:
  #     - POSTGRES_USER=${POSTGRES_USER}
  #     - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
  #     - POSTGRES_DB=${POSTGRES_DB}
  #   volumes:
  #     - postgres-data:/var/lib/postgresql/data
  #   restart: unless-stopped
  #   networks:
  #     - app-network

  # pgadmin:
  #   image: dpage/pgadmin4
  #   ports:
  #     - "5050:80"
  #   environment:
  #     - PGADMIN_DEFAULT_EMAIL=${PGADMIN_EMAIL}
  #     - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_PASSWORD}
  #   depends_on:
  #     - postgres
  #   restart: unless-stopped
  #   networks:
  #     - app-network

  redisinsight:
    image: redislabs/redisinsight:1.13.1
    ports:
      - "8001:8001"
    volumes:
      - redisinsight-data:/db
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  redis-data:
  # postgres-data:
  redisinsight-data:
