{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_ENV=development next dev --turbopack --port 3001", "build": "cross-env NODE_ENV=production next build", "start": "cross-env NODE_ENV=production next start --port 3001", "lint": "cross-env NODE_ENV=development next lint"}, "dependencies": {"axios": "^1.10.0", "cross-env": "^7.0.3", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}