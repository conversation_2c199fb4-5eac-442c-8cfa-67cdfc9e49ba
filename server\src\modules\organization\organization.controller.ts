import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
} from '@nestjs/common';
import { Public } from '../../common/decorators/public.decorator';
import { Organization } from './entities/organizations.entity';
import { OrganizationService } from './organization.service';
import {
  ApiOperation,
  ApiParam,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiTags,
  ApiCreatedResponse,
  ApiResponse,
} from '@nestjs/swagger';
import { CreateOrganizationDto } from './create-organization.dto';

@ApiTags('Organizations')
@Controller('organization')
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  @Public()
  @Get(':subdomain')
  @ApiOperation({ summary: 'Get organization by subdomain' })
  @ApiParam({
    name: 'subdomain',
    type: String,
    description: 'The subdomain (e.g. oxford)',
  })
  @ApiOkResponse({
    description: 'Organization retrieved successfully',
    type: Organization,
  })
  @ApiNotFoundResponse({ description: 'Organization not found' })
  async findOrg(@Param('subdomain') subdomain: string): Promise<Organization> {
    const org = await this.organizationService.findOrgBySubdomain(subdomain);
    if (!org) {
      throw new NotFoundException('Organization not found');
    }
    return org;
  }

  @Post('create-Organization')
  @ApiOperation({
    summary: 'Create a new organization and assign subscription',
  })
  @ApiCreatedResponse({
    description: 'Organization created successfully',
    type: Organization, // Use a DTO if returning a subset of fields
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  CreateOrganizationDto(
    @Body() createOrganizationdto: CreateOrganizationDto,
  ): Promise<Organization> {
    return this.organizationService.createOrganizationWithSubscription(
      createOrganizationdto,
    );
  }
}
