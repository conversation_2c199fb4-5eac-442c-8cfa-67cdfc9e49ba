import { ApiProperty } from "@nestjs/swagger";

export class CreateOrganizationDto {

     @ApiProperty({
    description: 'Name of the organization',
    example: 'Oxford Academy',
  })
  name: string;

  @ApiProperty({
    description: 'Primary contact email for the organization',
    example: '<EMAIL>',
  })
  contactEmail: string;

  @ApiProperty({
    description: 'Contact phone number of the organization',
    example: '+************',
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Country where the organization is located',
    example: 'United Kingdom',
  })
  country: string;

  // Subscription Details

  @ApiProperty({
    description: 'Subscription plan ID to assign to the organization',
    example: 'uuid-of-gold-plan',
  })
  subscriptionPlanId: string;

  @ApiProperty({
    description: 'Start date of the subscription',
    example: '2025-08-01T00:00:00.000Z',
  })
  startDate: Date;

  @ApiProperty({
    description: 'End date of the subscription',
    example: '2026-07-31T23:59:59.000Z',
  })
  endDate: Date;

  @ApiProperty({
    description: 'Whether the subscription is a trial',
    example: true,
    required: false,
  })
  isTrial?: boolean;
}

    
    
